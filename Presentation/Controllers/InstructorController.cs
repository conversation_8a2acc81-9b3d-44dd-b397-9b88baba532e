using System.Security.Claims;
using Application.DTOs;
using Application.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Presentation.Controllers;

[ApiController]
[Route("api/[controller]")]
public class InstructorController(IInstructorService instructorService) : ControllerBase
{
    private readonly IInstructorService _instructorService = instructorService;

    /// <summary>
    /// Login endpoint for instructors
    /// </summary>
    [HttpPost("login")]
    public async Task<ActionResult<AuthResponseDto>> Login([FromBody] LoginDto loginDto)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        var result = await _instructorService.LoginAsync(loginDto);
        if (result == null)
        {
            return Unauthorized(new { message = "Invalid email or password" });
        }

        return Ok(result);
    }

    /// <summary>
    /// Get all instructors (Admin only)
    /// </summary>
    [HttpGet]
    [Authorize]
    public async Task<ActionResult<IEnumerable<InstructorDto>>> GetAllInstructors()
    {
        var instructors = await _instructorService.GetAllInstructorsAsync();
        return Ok(instructors);
    }

    /// <summary>
    /// Get instructor by ID
    /// </summary>
    [HttpGet("{id}")]
    [Authorize]
    public async Task<ActionResult<InstructorDto>> GetInstructorById(Guid id)
    {
        var instructor = await _instructorService.GetInstructorByIdAsync(id);
        if (instructor == null)
        {
            return NotFound(new { message = "Instructor not found" });
        }

        return Ok(instructor);
    }

    /// <summary>
    /// Get current instructor's profile
    /// </summary>
    [HttpGet("profile")]
    [Authorize]
    public async Task<ActionResult<InstructorDto>> GetProfile()
    {
        var email = User.FindFirst(ClaimTypes.Email)?.Value;
        if (string.IsNullOrEmpty(email))
        {
            return Unauthorized(new { message = "Invalid token" });
        }

        var instructor = await _instructorService.GetInstructorByEmailAsync(email);
        if (instructor == null)
        {
            return NotFound(new { message = "Instructor not found" });
        }

        return Ok(instructor);
    }

    /// <summary>
    /// Create a new instructor (Temporarily open for testing)
    /// </summary>
    [HttpPost]
    // [Authorize(Roles = "Admin")] // Temporarily commented for testing
    public async Task<ActionResult<InstructorDto>> CreateInstructor(
        [FromBody] CreateInstructorDto createInstructorDto
    )
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        try
        {
            var instructor = await _instructorService.CreateInstructorAsync(createInstructorDto);
            return CreatedAtAction(
                nameof(GetInstructorById),
                new { id = instructor.Id },
                instructor
            );
        }
        catch (InvalidOperationException ex)
        {
            return Conflict(new { message = ex.Message });
        }
    }

    /// <summary>
    /// Update instructor information
    /// </summary>
    [HttpPut("{id}")]
    [Authorize]
    public async Task<ActionResult<InstructorDto>> UpdateInstructor(
        Guid id,
        [FromBody] UpdateInstructorDto updateInstructorDto
    )
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        // Check if user is updating their own profile or is an admin
        var currentUserEmail = User.FindFirst(ClaimTypes.Email)?.Value;
        var currentUserRole = User.FindFirst(ClaimTypes.Role)?.Value;
        var targetInstructor = await _instructorService.GetInstructorByIdAsync(id);

        if (targetInstructor == null)
        {
            return NotFound(new { message = "Instructor not found" });
        }

        if (currentUserRole != "Admin" && currentUserEmail != targetInstructor.Email)
        {
            return Forbid("You can only update your own profile");
        }

        try
        {
            var updatedInstructor = await _instructorService.UpdateInstructorAsync(
                id,
                updateInstructorDto
            );
            return Ok(updatedInstructor);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    /// <summary>
    /// Delete instructor (Admin only)
    /// </summary>
    [HttpDelete("{id}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> DeleteInstructor(Guid id)
    {
        var result = await _instructorService.DeleteInstructorAsync(id);
        if (!result)
        {
            return NotFound(new { message = "Instructor not found" });
        }

        return NoContent();
    }

    /// <summary>
    /// Check if email exists
    /// </summary>
    [HttpGet("check-email/{email}")]
    public async Task<ActionResult<bool>> CheckEmailExists(string email)
    {
        var exists = await _instructorService.EmailExistsAsync(email);
        return Ok(new { exists });
    }
}
