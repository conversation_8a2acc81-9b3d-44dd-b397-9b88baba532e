using System.ComponentModel.DataAnnotations;

namespace Domain.Entities;

public class InstructorEntity
{
    [Key]
    public Guid Id { get; set; } = Guid.NewGuid();

    [StringLength(50, ErrorMessage = "First name must be less than 50 characters")]
    [Required(ErrorMessage = "First name is required")]
    [MinLength(2, ErrorMessage = "First name must be at least 2 characters")]
    public string FirstName { get; set; } = null!;

    [StringLength(50, ErrorMessage = "Last name must be less than 50 characters")]
    [Required(ErrorMessage = "Last name is required")]
    [MinLength(2, ErrorMessage = "Last name must be at least 2 characters")]
    public string LastName { get; set; } = null!;

    [EmailAddress(ErrorMessage = "Invalid email address")]
    [Required(ErrorMessage = "Email is required")]
    public string Email { get; set; } = null!;

    [Phone(ErrorMessage = "Invalid phone number")]
    [Required(ErrorMessage = "Phone is required")]
    public string Phone { get; set; } = null!;

    [Required(ErrorMessage = "Location is required")]
    [StringLength(100, ErrorMessage = "Location must be less than 100 characters")]
    public string Location { get; set; } = null!;

    public string? ProfilePicture { get; set; }

    [Required(ErrorMessage = "Role is required")]
    [StringLength(50, ErrorMessage = "Role must be less than 50 characters")]
    public string Role { get; set; } = "Instructor";

    [Required(ErrorMessage = "Password is required")]
    [StringLength(255, ErrorMessage = "Password must be less than 255 characters")]
    [MinLength(8, ErrorMessage = "Password must be at least 8 characters")]
    public string PasswordHash { get; set; } = null!;

    [Required]
    public bool IsActive { get; set; } = true;

    [Required]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    public DateTime? LastLoginAt { get; set; }
}
