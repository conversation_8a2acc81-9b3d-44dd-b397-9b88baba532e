using Application.Interfaces;
using Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Persistence.Data;

namespace Persistence.Repositories;

public class InstructorRepository(AppDbContext context) : IInstructorRepository
{
    private readonly AppDbContext _context = context;

    public async Task<IEnumerable<InstructorEntity>> GetAllAsync()
    {
        return await _context
            .Instructors.Where(i => i.IsActive)
            .OrderBy(i => i.FirstName)
            .ThenBy(i => i.LastName)
            .ToListAsync();
    }

    public async Task<InstructorEntity?> GetByIdAsync(Guid id)
    {
        return await _context.Instructors.FirstOrDefaultAsync(i => i.Id == id && i.IsActive);
    }

    public async Task<InstructorEntity?> GetByEmailAsync(string email)
    {
        return await _context.Instructors.FirstOrDefaultAsync(i =>
            i.Email.ToLower() == email.ToLower() && i.<PERSON>ctive
        );
    }

    public async Task<InstructorEntity> CreateAsync(InstructorEntity instructor)
    {
        instructor.Id = Guid.NewGuid();
        instructor.CreatedAt = DateTime.UtcNow;
        instructor.IsActive = true;

        _context.Instructors.Add(instructor);
        await _context.SaveChangesAsync();
        return instructor;
    }

    public async Task<InstructorEntity> UpdateAsync(InstructorEntity instructor)
    {
        var existingInstructor = await _context.Instructors.FirstOrDefaultAsync(i =>
            i.Id == instructor.Id
        );

        if (existingInstructor == null)
        {
            throw new InvalidOperationException($"Instructor with ID {instructor.Id} not found");
        }

        // Update properties
        existingInstructor.FirstName = instructor.FirstName;
        existingInstructor.LastName = instructor.LastName;
        existingInstructor.Email = instructor.Email;
        existingInstructor.Phone = instructor.Phone;
        existingInstructor.Location = instructor.Location;
        existingInstructor.ProfilePicture = instructor.ProfilePicture;
        existingInstructor.Role = instructor.Role;
        existingInstructor.IsActive = instructor.IsActive;

        // Only update password if it's provided
        if (!string.IsNullOrEmpty(instructor.PasswordHash))
        {
            existingInstructor.PasswordHash = instructor.PasswordHash;
        }

        await _context.SaveChangesAsync();
        return existingInstructor;
    }

    public async Task<bool> DeleteAsync(Guid id)
    {
        var instructor = await _context.Instructors.FirstOrDefaultAsync(i => i.Id == id);

        if (instructor == null)
        {
            return false;
        }

        // Soft delete - set IsActive to false
        instructor.IsActive = false;
        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<bool> ExistsAsync(Guid id)
    {
        return await _context.Instructors.AnyAsync(i => i.Id == id && i.IsActive);
    }

    public async Task<bool> EmailExistsAsync(string email)
    {
        return await _context.Instructors.AnyAsync(i =>
            i.Email.ToLower() == email.ToLower() && i.IsActive
        );
    }
}
