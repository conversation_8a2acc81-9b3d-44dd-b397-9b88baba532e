using System.Security.Cryptography;
using System.Text;
using Application.DTOs;
using Application.Interfaces;
using Application.Models;
using Domain.Entities;

namespace Application.Services;

public class InstructorService(IInstructorRepository instructorRepository, IJwtService jwtService)
    : IInstructorService
{
    private readonly IInstructorRepository _instructorRepository = instructorRepository;
    private readonly IJwtService _jwtService = jwtService;

    public async Task<IEnumerable<InstructorDto>> GetAllInstructorsAsync()
    {
        var instructors = await _instructorRepository.GetAllAsync();
        return instructors.Select(MapToDto);
    }

    public async Task<InstructorDto?> GetInstructorByIdAsync(Guid id)
    {
        var instructor = await _instructorRepository.GetByIdAsync(id);
        return instructor != null ? MapToDto(instructor) : null;
    }

    public async Task<InstructorDto?> GetInstructorByEmailAsync(string email)
    {
        var instructor = await _instructorRepository.GetByEmailAsync(email);
        return instructor != null ? MapToDto(instructor) : null;
    }

    public async Task<InstructorDto> CreateInstructorAsync(CreateInstructorDto createInstructorDto)
    {
        // Check if email already exists
        if (await _instructorRepository.EmailExistsAsync(createInstructorDto.Email))
        {
            throw new InvalidOperationException("An instructor with this email already exists");
        }

        var instructorEntity = new InstructorEntity
        {
            FirstName = createInstructorDto.FirstName,
            LastName = createInstructorDto.LastName,
            Email = createInstructorDto.Email,
            Phone = createInstructorDto.Phone,
            Location = createInstructorDto.Location,
            ProfilePicture = createInstructorDto.ProfilePicture,
            Role = createInstructorDto.Role,
            PasswordHash = HashPassword(createInstructorDto.Password),
        };

        var createdInstructor = await _instructorRepository.CreateAsync(instructorEntity);
        return MapToDto(createdInstructor);
    }

    public async Task<InstructorDto> UpdateInstructorAsync(
        Guid id,
        UpdateInstructorDto updateInstructorDto
    )
    {
        var existingInstructor = await _instructorRepository.GetByIdAsync(id);
        if (existingInstructor == null)
        {
            throw new InvalidOperationException($"Instructor with ID {id} not found");
        }

        // Check if email is being changed and if it already exists
        if (
            !string.IsNullOrEmpty(updateInstructorDto.Email)
            && updateInstructorDto.Email != existingInstructor.Email
            && await _instructorRepository.EmailExistsAsync(updateInstructorDto.Email)
        )
        {
            throw new InvalidOperationException("An instructor with this email already exists");
        }

        // Update only provided fields
        if (!string.IsNullOrEmpty(updateInstructorDto.FirstName))
            existingInstructor.FirstName = updateInstructorDto.FirstName;

        if (!string.IsNullOrEmpty(updateInstructorDto.LastName))
            existingInstructor.LastName = updateInstructorDto.LastName;

        if (!string.IsNullOrEmpty(updateInstructorDto.Email))
            existingInstructor.Email = updateInstructorDto.Email;

        if (!string.IsNullOrEmpty(updateInstructorDto.Phone))
            existingInstructor.Phone = updateInstructorDto.Phone;

        if (!string.IsNullOrEmpty(updateInstructorDto.Location))
            existingInstructor.Location = updateInstructorDto.Location;

        if (updateInstructorDto.ProfilePicture != null)
            existingInstructor.ProfilePicture = updateInstructorDto.ProfilePicture;

        if (!string.IsNullOrEmpty(updateInstructorDto.Role))
            existingInstructor.Role = updateInstructorDto.Role;

        if (updateInstructorDto.IsActive.HasValue)
            existingInstructor.IsActive = updateInstructorDto.IsActive.Value;

        var updatedInstructor = await _instructorRepository.UpdateAsync(existingInstructor);
        return MapToDto(updatedInstructor);
    }

    public async Task<bool> DeleteInstructorAsync(Guid id)
    {
        return await _instructorRepository.DeleteAsync(id);
    }

    public async Task<AuthResponseDto?> LoginAsync(LoginDto loginDto)
    {
        var instructor = await _instructorRepository.GetByEmailAsync(loginDto.Email);
        if (instructor == null || !VerifyPassword(loginDto.Password, instructor.PasswordHash))
        {
            return null;
        }

        // Update last login time
        instructor.LastLoginAt = DateTime.UtcNow;
        await _instructorRepository.UpdateAsync(instructor);

        // Generate JWT token
        var token = _jwtService.GenerateToken(MapToDto(instructor));

        return new AuthResponseDto
        {
            Token = token,
            ExpiresAt = DateTime.UtcNow.AddHours(24),
            Instructor = MapToDto(instructor),
        };
    }

    public async Task<bool> ValidatePasswordAsync(string email, string password)
    {
        var instructor = await _instructorRepository.GetByEmailAsync(email);
        return instructor != null && VerifyPassword(password, instructor.PasswordHash);
    }

    public async Task<bool> EmailExistsAsync(string email)
    {
        return await _instructorRepository.EmailExistsAsync(email);
    }

    private static InstructorDto MapToDto(InstructorEntity entity)
    {
        return new InstructorDto
        {
            Id = entity.Id,
            FirstName = entity.FirstName,
            LastName = entity.LastName,
            Email = entity.Email,
            Phone = entity.Phone,
            Location = entity.Location,
            ProfilePicture = entity.ProfilePicture,
            Role = entity.Role,
            IsActive = entity.IsActive,
            CreatedAt = entity.CreatedAt,
            LastLoginAt = entity.LastLoginAt,
        };
    }

    private static string HashPassword(string password)
    {
        using var sha256 = SHA256.Create();
        var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password));
        return Convert.ToBase64String(hashedBytes);
    }

    private static bool VerifyPassword(string password, string hashedPassword)
    {
        var hashedInput = HashPassword(password);
        return hashedInput == hashedPassword;
    }
}
