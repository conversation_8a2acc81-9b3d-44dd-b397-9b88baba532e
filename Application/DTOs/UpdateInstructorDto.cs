using System.ComponentModel.DataAnnotations;

namespace Application.DTOs;

public class UpdateInstructorDto
{
    [StringLength(50, ErrorMessage = "First name must be less than 50 characters")]
    [MinLength(2, ErrorMessage = "First name must be at least 2 characters")]
    public string? FirstName { get; set; }

    [StringLength(50, ErrorMessage = "Last name must be less than 50 characters")]
    [MinLength(2, ErrorMessage = "Last name must be at least 2 characters")]
    public string? LastName { get; set; }

    [EmailAddress(ErrorMessage = "Invalid email address")]
    public string? Email { get; set; }

    [Phone(ErrorMessage = "Invalid phone number")]
    public string? Phone { get; set; }

    [StringLength(100, ErrorMessage = "Location must be less than 100 characters")]
    public string? Location { get; set; }

    public string? ProfilePicture { get; set; }

    [StringLength(50, ErrorMessage = "Role must be less than 50 characters")]
    public string? Role { get; set; }

    public bool? IsActive { get; set; }
}
