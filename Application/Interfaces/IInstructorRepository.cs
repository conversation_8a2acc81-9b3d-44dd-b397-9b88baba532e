using Domain.Entities;

namespace Application.Interfaces;

public interface IInstructorRepository
{
    Task<IEnumerable<InstructorEntity>> GetAllAsync();
    Task<InstructorEntity?> GetByIdAsync(Guid id);
    Task<InstructorEntity?> GetByEmailAsync(string email);
    Task<InstructorEntity> <PERSON><PERSON><PERSON><PERSON>(InstructorEntity instructor);
    Task<InstructorEntity> UpdateAsync(InstructorEntity instructor);
    Task<bool> DeleteAsync(Guid id);
    Task<bool> ExistsAsync(Guid id);
    Task<bool> EmailExistsAsync(string email);
}
