using Application.DTOs;

namespace Application.Interfaces;

public interface IInstructorService
{
    Task<IEnumerable<InstructorDto>> GetAllInstructorsAsync();
    Task<InstructorDto?> GetInstructorByIdAsync(Guid id);
    Task<InstructorDto?> GetInstructorByEmailAsync(string email);
    Task<InstructorDto> CreateInstructorAsync(CreateInstructorDto createInstructorDto);
    Task<InstructorDto> UpdateInstructorAsync(Guid id, UpdateInstructorDto updateInstructorDto);
    Task<bool> DeleteInstructorAsync(Guid id);
    Task<AuthResponseDto?> LoginAsync(LoginDto loginDto);
    Task<bool> ValidatePasswordAsync(string email, string password);
    Task<bool> EmailExistsAsync(string email);
}
