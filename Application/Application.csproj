﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <Folder Include="DTOs\" />
      <Folder Include="Interfaces\" />
      <Folder Include="Models\" />
      <Folder Include="Services\" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\Domain\Domain.csproj" />
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.9" />
      <PackageReference Include="Microsoft.IdentityModel.Tokens" Version="8.14.0" />
      <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.14.0" />
    </ItemGroup>

</Project>
